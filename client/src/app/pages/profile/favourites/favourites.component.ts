import {ProfileService} from "@/services/profile.service";
import {Ng<PERSON><PERSON>, NgIf, NgOptimizedImage, isPlatformBrowser} from "@angular/common";
import {Component, ElementRef, inject, ViewChild, signal, computed, OnInit, OnDestroy, PLATFORM_ID, ChangeDetectionStrategy} from '@angular/core';
import {environment} from "@/env/environment";
import {Router, ActivatedRoute} from "@angular/router";
import {LibraryService} from "@/services/library.service";
import { ToasterService } from "@/services/toaster.service";
import { ContentService } from "@/services/content.service";
import { CommonModule } from '@angular/common';
import {PhotoFavouritesComponent} from "@/pages/profile/favourites/photo-favourites/photo-favourites.component";
import {LibraryFavouritesComponent} from "@/pages/profile/favourites/library-favourites/library-favourites.component";
import {QuoteFavouritesComponent} from "@/pages/profile/favourites/quote-favourites/quote-favourites.component";
import {ContentFavouritesComponent} from "@/pages/profile/favourites/content-favourites/content-favourites.component";
import {ForumFavouritesComponent} from "@/pages/profile/favourites/forum-favourites/forum-favourites.component";

@Component({
  selector: 'app-favourites',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgClass,
    NgIf,
    CommonModule,
    PhotoFavouritesComponent,
    LibraryFavouritesComponent,
    QuoteFavouritesComponent,
    ContentFavouritesComponent,
    ForumFavouritesComponent
  ],
  templateUrl: './favourites.component.html',
  styleUrl: './favourites.component.scss'
})
export class FavouritesComponent implements OnInit, OnDestroy {
  // Services
  private profileService = inject(ProfileService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private libraryService = inject(LibraryService);
  private contentService = inject(ContentService);
  private toasterService = inject(ToasterService);
  private platformId = inject(PLATFORM_ID);

  // ViewChild for tab content container
  @ViewChild('tabContentContainer', { static: false }) tabContentContainer?: ElementRef<HTMLElement>;

  // Constants
  protected readonly environment = environment;

  // Tab configuration
  readonly tabsList = [
    {
      label: 'Фотогалерея',
      value: 'photo',
    },
    {
      label: 'Книги',
      value: 'library',
    },
    {
      label: 'Цитаты',
      value: 'quote',
    },
    {
      label: 'Статьи',
      value: 'content',
    },
    {
      label: 'Форум',
      value: 'forum',
    },
  ] as const;

  // Signals
  selectedTab = signal(this.tabsList[0]);
  selectedDropdElement = signal<any>(null);

  // Computed values
  currentTabIndex = computed(() =>
    this.tabsList.findIndex(tab => tab.value === this.selectedTab().value)
  );

  canGoNext = computed(() =>
    this.currentTabIndex() < this.tabsList.length - 1
  );

  canGoPrev = computed(() =>
    this.currentTabIndex() > 0
  );

  // Touch gesture properties
  private touchStartX = 0;
  private touchStartY = 0;
  private touchEndX = 0;
  private touchEndY = 0;
  private readonly swipeThreshold = 50; // Minimum distance for swipe
  private readonly swipeAngleThreshold = 30; // Maximum angle deviation for horizontal swipe
  private isTouchDevice = false;

  ngOnInit(): void {
    this.profileService.getProfile().subscribe();

    // Подписываемся на изменения параметров роута для определения активного саб-таба
    this.route.params.subscribe(params => {
      const subtab = params['subtab'];
      if (subtab) {
        const tab = this.tabsList.find(t => t.value === subtab);
        if (tab) {
          this.selectedTab = tab;
        } else {
          // Если саб-таб не найден, перенаправляем на первый таб
          this.navigateToSubTab(this.tabsList[0].value);
        }
      } else {
        // Если нет параметра subtab, перенаправляем на первый саб-таб
        this.navigateToSubTab(this.tabsList[0].value);
      }
    });
  }

  selectSubTab(tab: any): void {
    this.navigateToSubTab(tab.value);
  }

  private navigateToSubTab(subTabValue: string): void {
    this.router.navigate(['/ru/profile/favorites', subTabValue]);
  }

  showNext() {
    const currentIndex = this.tabsList.findIndex(tab => tab.value === this.selectedTab.value);
    if (currentIndex < this.tabsList.length - 1) {
      this.navigateToSubTab(this.tabsList[currentIndex + 1].value);
    }
  }

  showPrev() {
    const currentIndex = this.tabsList.findIndex(tab => tab.value === this.selectedTab.value);
    if (currentIndex > 0) {
      this.navigateToSubTab(this.tabsList[currentIndex - 1].value);
    }
  }
}
